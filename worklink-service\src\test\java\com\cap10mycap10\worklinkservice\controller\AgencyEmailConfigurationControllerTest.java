package com.cap10mycap10.worklinkservice.controller;

import com.cap10mycap10.worklinkservice.dto.email.AgencyEmailConfigurationPublicDto;
import com.cap10mycap10.worklinkservice.dto.email.AgencyEmailConfigurationResponseDto;
import com.cap10mycap10.worklinkservice.model.AgencyEmailConfiguration;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Assertions;

/**
 * Test class to verify SMTP password encryption functionality
 */
public class AgencyEmailConfigurationControllerTest {

    @Test
    public void testPublicDtoMasksPassword() {
        // Given
        AgencyEmailConfiguration config = AgencyEmailConfiguration.builder()
                .id(1L)
                .agencyId(1L)
                .smtpHost("smtp.hostinger.com")
                .smtpPort(465)
                .smtpUsername("<EMAIL>")
                .smtpPassword("MySecretPassword123")
                .fromEmail("<EMAIL>")
                .fromName("Test Agency")
                .build();

        // When
        AgencyEmailConfigurationPublicDto publicDto = AgencyEmailConfigurationPublicDto.fromEntity(config);

        // Then
        assert publicDto.getSmtpPassword().equals("[ENCRYPTED]");
        assert publicDto.getSmtpHost().equals("smtp.hostinger.com");
        assert publicDto.getSmtpUsername().equals("<EMAIL>");
    }

    @Test
    public void testResponseDtoIncludesDecryptedPassword() {
        // Given
        AgencyEmailConfiguration config = AgencyEmailConfiguration.builder()
                .id(1L)
                .agencyId(1L)
                .smtpHost("smtp.hostinger.com")
                .smtpPort(465)
                .smtpUsername("<EMAIL>")
                .smtpPassword("MySecretPassword123")
                .fromEmail("<EMAIL>")
                .fromName("Test Agency")
                .build();

        // When
        AgencyEmailConfigurationResponseDto responseDto = AgencyEmailConfigurationResponseDto.fromEntity(config);

        // Then
        assert responseDto.getSmtpPassword().equals("MySecretPassword123");
        assert responseDto.getSmtpHost().equals("smtp.hostinger.com");
        assert responseDto.getSmtpUsername().equals("<EMAIL>");
    }
}
