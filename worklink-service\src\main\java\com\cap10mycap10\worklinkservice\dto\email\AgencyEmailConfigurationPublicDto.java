package com.cap10mycap10.worklinkservice.dto.email;

import com.cap10mycap10.worklinkservice.model.AgencyEmailConfiguration;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * DTO for returning agency email configuration data via public API.
 * This DTO masks the SMTP password for security purposes while providing
 * all other configuration details needed for external consumption.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AgencyEmailConfigurationPublicDto {

    private Long id;
    private Long agencyId;
    private String smtpHost;
    private Integer smtpPort;
    private String smtpUsername;
    
    /**
     * The SMTP password is masked for security.
     * External API consumers should not have access to the actual password.
     */
    private String smtpPassword = "[ENCRYPTED]";
    
    private String fromEmail;
    private String fromName;
    private String replyToEmail;
    private Boolean smtpAuth;
    private Boolean smtpStarttlsEnable;
    private Boolean smtpStarttlsRequired;
    private Boolean smtpSslEnable;
    private String smtpSslSocketFactoryClass;
    private String supportEmail;
    private String websiteUrl;
    private String logoUrl;
    private Boolean isActive;
    private Boolean isVerified;
    private LocalDateTime lastTestDate;
    private String lastTestResult;
    private String notes;
    private java.time.Instant createdDate;
    private LocalDateTime lastModifiedDate;
    private String createdBy;
    private String lastModifiedBy;

    /**
     * Create a public DTO from an AgencyEmailConfiguration entity.
     * The password will be masked for security.
     *
     * @param config the email configuration entity
     * @return the public DTO with masked password
     */
    public static AgencyEmailConfigurationPublicDto fromEntity(AgencyEmailConfiguration config) {
        if (config == null) {
            return null;
        }

        return AgencyEmailConfigurationPublicDto.builder()
                .id(config.getId())
                .agencyId(config.getAgencyId())
                .smtpHost(config.getSmtpHost())
                .smtpPort(config.getSmtpPort())
                .smtpUsername(config.getSmtpUsername())
                .smtpPassword("[ENCRYPTED]") // Always mask the password
                .fromEmail(config.getFromEmail())
                .fromName(config.getFromName())
                .replyToEmail(config.getReplyToEmail())
                .smtpAuth(config.getSmtpAuth())
                .smtpStarttlsEnable(config.getSmtpStarttlsEnable())
                .smtpStarttlsRequired(config.getSmtpStarttlsRequired())
                .smtpSslEnable(config.getSmtpSslEnable())
                .smtpSslSocketFactoryClass(config.getSmtpSslSocketFactoryClass())
                .supportEmail(config.getSupportEmail())
                .websiteUrl(config.getWebsiteUrl())
                .logoUrl(config.getLogoUrl())
                .isActive(config.getIsActive())
                .isVerified(config.getIsVerified())
                .lastTestDate(config.getLastTestDate())
                .lastTestResult(config.getLastTestResult())
                .notes(config.getNotes())
                .createdDate(config.getCreatedDate())
                .lastModifiedDate(config.getLastModifiedDate())
                .createdBy(config.getCreatedBy())
                .lastModifiedBy(config.getLastModifiedBy())
                .build();
    }

    /**
     * Override toString to avoid logging any sensitive information.
     */
    @Override
    public String toString() {
        return "AgencyEmailConfigurationPublicDto{" +
                "id=" + id +
                ", agencyId=" + agencyId +
                ", smtpHost='" + smtpHost + '\'' +
                ", smtpPort=" + smtpPort +
                ", smtpUsername='" + smtpUsername + '\'' +
                ", smtpPassword='[ENCRYPTED]'" +
                ", fromEmail='" + fromEmail + '\'' +
                ", fromName='" + fromName + '\'' +
                ", replyToEmail='" + replyToEmail + '\'' +
                ", smtpAuth=" + smtpAuth +
                ", smtpStarttlsEnable=" + smtpStarttlsEnable +
                ", smtpStarttlsRequired=" + smtpStarttlsRequired +
                ", smtpSslEnable=" + smtpSslEnable +
                ", smtpSslSocketFactoryClass='" + smtpSslSocketFactoryClass + '\'' +
                ", supportEmail='" + supportEmail + '\'' +
                ", websiteUrl='" + websiteUrl + '\'' +
                ", logoUrl='" + logoUrl + '\'' +
                ", isActive=" + isActive +
                ", isVerified=" + isVerified +
                ", lastTestDate=" + lastTestDate +
                ", lastTestResult='" + lastTestResult + '\'' +
                ", notes='" + notes + '\'' +
                ", createdDate=" + createdDate +
                ", lastModifiedDate=" + lastModifiedDate +
                ", createdBy='" + createdBy + '\'' +
                ", lastModifiedBy='" + lastModifiedBy + '\'' +
                '}';
    }
}
